'use client';

import { useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

export default function SentryTestPage() {
  const [lastAction, setLastAction] = useState<string>('');

  const throwError = () => {
    setLastAction('Error thrown');
    throw new Error('Bu bir test hatası! Sentry tarafından yakalanmalı.');
  };

  const captureException = () => {
    setLastAction('Exception captured');
    try {
      throw new Error('Manuel olarak yakalanan hata');
    } catch (error) {
      Sentry.captureException(error);
    }
  };

  const captureMessage = () => {
    setLastAction('Message captured');
    Sentry.captureMessage('Test mesajı - Sentry çalışıyor!', 'info');
  };

  const addBreadcrumb = () => {
    setLastAction('Breadcrumb added');
    Sentry.addBreadcrumb({
      message: 'Kullanıcı test breadcrumb ekledi',
      level: 'info',
      category: 'user-action',
    });
  };

  const setUserContext = () => {
    setLastAction('User context set');
    Sentry.setUser({
      id: '123',
      email: '<EMAIL>',
      username: 'test-user',
    });
  };

  const setTag = () => {
    setLastAction('Tag set');
    Sentry.setTag('test-tag', 'test-value');
  };

  const performanceTest = () => {
    setLastAction('Performance test started');

    // Use startSpan instead of startTransaction in v8
    Sentry.startSpan(
      {
        name: 'test-transaction',
        op: 'test',
      },
      () => {
        // Simulate some work
        return new Promise(resolve => {
          setTimeout(resolve, 1000);
        });
      }
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Sentry Test Sayfası</h1>
        <p className="text-muted-foreground">
          Sentry entegrasyonunu test etmek için aşağıdaki butonları kullanın
        </p>
        {lastAction && (
          <Badge variant="outline" className="mt-2">
            Son aksiyon: {lastAction}
          </Badge>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Hata Testi</CardTitle>
            <CardDescription>
              Yakalanmamış hata fırlatır
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={throwError} variant="destructive" className="w-full">
              Hata Fırlat
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Exception Yakalama</CardTitle>
            <CardDescription>
              Manuel olarak exception yakalar
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={captureException} variant="outline" className="w-full">
              Exception Yakala
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Mesaj Gönderme</CardTitle>
            <CardDescription>
              Sentryye bilgi mesajı gönderir
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={captureMessage} variant="default" className="w-full">
              Mesaj Gönder
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Breadcrumb Ekleme</CardTitle>
            <CardDescription>
              Debug için breadcrumb ekler
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={addBreadcrumb} variant="secondary" className="w-full">
              Breadcrumb Ekle
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Kullanıcı Context</CardTitle>
            <CardDescription>
              Kullanıcı bilgilerini ayarlar
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={setUserContext} variant="outline" className="w-full">
              User Context Ayarla
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Tag Ekleme</CardTitle>
            <CardDescription>
              Hata filtreleme için tag ekler
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={setTag} variant="secondary" className="w-full">
              Tag Ekle
            </Button>
          </CardContent>
        </Card>

        <Card className="md:col-span-2 lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-lg">Performance Test</CardTitle>
            <CardDescription>
              Performance monitoring testi yapar
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={performanceTest} variant="default" className="w-full">
              Performance Test Başlat
            </Button>
          </CardContent>
        </Card>
      </div>

      <Alert>
        <AlertDescription>
          <strong>Not:</strong> Bu testler Sentry dashboardunuzda görünecektir. 
          Development ortamında debug mode açık olduğu için console'da da logları görebilirsiniz.
        </AlertDescription>
      </Alert>
    </div>
  );
}
