'use client';

import Link from 'next/link';
import { AnimatedSection } from '@/components/ui/animated-section';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  Sparkles, 
  Zap, 
  CheckCircle, 
  Star,
  Shield,
  Clock,
  Users
} from 'lucide-react';

export function PremiumCTA() {
  const benefits = [
    {
      icon: CheckCircle,
      text: '7 gün ücretsiz deneme',
      color: 'text-green-600'
    },
    {
      icon: Shield,
      text: 'Kredi kartı bilgisi gerekmez',
      color: 'text-blue-600'
    },
    {
      icon: Clock,
      text: 'Anlık kurulum ve başlangıç',
      color: 'text-purple-600'
    },
    {
      icon: Users,
      text: '24/7 ücretsiz destek',
      color: 'text-orange-600'
    }
  ];

  return (
    <section className="relative isolate overflow-hidden py-24 md:py-32">
      {/* Enhanced background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-primary/10 to-secondary/20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,hsl(var(--primary)/0.15),transparent_70%)]" />
        <div className="absolute inset-0 bg-grid-pattern opacity-20" />
      </div>

      {/* Floating decorative elements */}
      <div className="absolute top-20 left-10 h-32 w-32 rounded-full bg-gradient-to-br from-primary/20 to-secondary/10 blur-3xl" />
      <div className="absolute bottom-20 right-10 h-24 w-24 rounded-full bg-gradient-to-br from-secondary/20 to-primary/10 blur-2xl" />

      <div className="container mx-auto px-4 relative">
        <AnimatedSection animation="fade-up" delay={100}>
          <div className="mx-auto max-w-4xl text-center">
            {/* Premium badge */}
            <Badge 
              variant="outline" 
              className="mb-8 px-6 py-3 text-base font-medium border-primary/30 bg-primary/10 text-primary backdrop-blur-sm"
            >
              <Sparkles className="mr-2 h-5 w-5" />
              Hemen Başlayın
            </Badge>

            <h2 className="mb-6 bg-gradient-to-r from-foreground via-foreground to-foreground/80 bg-clip-text text-4xl font-bold text-balance text-transparent md:text-6xl">
              Spor Salonunuzu
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Dijital Dönüştürün
              </span>
            </h2>
            
            <p className="mx-auto mt-6 max-w-[70ch] text-xl leading-relaxed text-muted-foreground">
              Kurumsal güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve otomasyonla 
              operasyonunuzu bir üst seviyeye taşıyın. Rakiplerinizden önde olmak için 
              <span className="font-semibold text-primary"> bugün başlayın!</span>
            </p>

            {/* CTA Buttons */}
            <div className="mt-12 flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
              <Button
                asChild
                size="lg"
                className="group h-16 px-10 text-lg font-semibold shadow-2xl transition-all duration-300 hover:shadow-3xl hover:scale-105"
              >
                <Link href="/onboarding">
                  <Zap className="mr-3 h-6 w-6" />
                  Ücretsiz Denemeyi Başlat
                  <ArrowRight className="ml-3 h-6 w-6 transition-transform group-hover:translate-x-2" />
                </Link>
              </Button>
              
              <Button
                asChild
                size="lg"
                variant="outline"
                className="h-16 border-2 border-primary/30 bg-background/80 px-10 text-lg font-semibold backdrop-blur hover:bg-primary/10 hover:border-primary/50"
              >
                <Link href="/features">
                  Tüm Özellikleri Keşfedin
                </Link>
              </Button>
            </div>

            {/* Trust indicators in grid */}
            <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {benefits.map((benefit, index) => {
                const IconComponent = benefit.icon;
                return (
                  <div
                    key={index}
                    className="flex flex-col items-center gap-3 rounded-xl border border-border/50 bg-background/80 p-6 backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-lg"
                  >
                    <div className={`rounded-lg bg-primary/10 p-3`}>
                      <IconComponent className={`h-6 w-6 ${benefit.color}`} />
                    </div>
                    <span className="text-sm font-medium text-center leading-tight">
                      {benefit.text}
                    </span>
                  </div>
                );
              })}
            </div>

            {/* Bottom trust line */}
            <div className="mt-12 flex flex-col items-center gap-6 text-sm text-muted-foreground sm:flex-row sm:justify-center">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">4.9/5 puan (500+ değerlendirme)</span>
              </div>
              <div className="hidden sm:block h-4 w-px bg-border" />
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                <span>180+ spor salonu aktif kullanıyor</span>
              </div>
              <div className="hidden sm:block h-4 w-px bg-border" />
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-blue-500" />
                <span>KVKV uyumlu ve güvenli</span>
              </div>
            </div>

            {/* Urgency element */}
            <div className="mt-8 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-200/50 px-4 py-2 text-sm">
              <div className="h-2 w-2 rounded-full bg-orange-500 animate-pulse" />
              <span className="font-medium text-orange-700 dark:text-orange-400">
                Bu ay sadece 50 yeni salon kaydı alıyoruz!
              </span>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}