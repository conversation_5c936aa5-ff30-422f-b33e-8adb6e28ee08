import * as Sentry from '@sentry/nextjs';

/**
 * Sentry utility functions for consistent error handling and monitoring
 */

/**
 * Capture an exception with additional context
 */
export function captureException(
  error: Error,
  context?: {
    user?: Sentry.User;
    tags?: Record<string, string>;
    extra?: Record<string, any>;
    level?: Sentry.SeverityLevel;
  }
) {
  return Sentry.captureException(error, {
    level: context?.level || 'error',
    tags: context?.tags,
    extra: context?.extra,
    user: context?.user,
  });
}

/**
 * Capture a message with context
 */
export function captureMessage(
  message: string,
  level: Sentry.SeverityLevel = 'info',
  context?: {
    tags?: Record<string, string>;
    extra?: Record<string, any>;
  }
) {
  return Sentry.captureMessage(message, {
    level,
    tags: context?.tags,
    extra: context?.extra,
  });
}

/**
 * Add breadcrumb for debugging
 */
export function addBreadcrumb(
  message: string,
  category: string = 'custom',
  level: Sentry.SeverityLevel = 'info',
  data?: Record<string, any>
) {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data,
    timestamp: Date.now() / 1000,
  });
}

/**
 * Set user context
 */
export function setUser(user: {
  id?: string;
  email?: string;
  username?: string;
  [key: string]: any;
}) {
  Sentry.setUser(user);
}

/**
 * Set tags for filtering
 */
export function setTags(tags: Record<string, string>) {
  Object.entries(tags).forEach(([key, value]) => {
    Sentry.setTag(key, value);
  });
}

/**
 * Set extra context
 */
export function setExtra(key: string, value: any) {
  Sentry.setExtra(key, value);
}

/**
 * Start a performance span (replaces startTransaction in v8)
 */
export function startSpan<T>(
  context: { name: string; op?: string },
  callback: () => T
): T {
  return Sentry.startSpan(context, callback);
}

/**
 * Wrap a function with performance monitoring
 */
export function withPerformance<T extends (...args: any[]) => any>(
  name: string,
  fn: T,
  op: string = 'function'
): T {
  return ((...args: Parameters<T>) => {
    return Sentry.startSpan(
      {
        name,
        op,
      },
      () => fn(...args)
    );
  }) as T;
}

/**
 * Wrap an async function with performance monitoring
 */
export function withAsyncPerformance<T extends (...args: any[]) => Promise<any>>(
  name: string,
  fn: T,
  op: string = 'async-function'
): T {
  return (async (...args: Parameters<T>) => {
    return Sentry.startSpan(
      {
        name,
        op,
      },
      () => fn(...args)
    );
  }) as T;
}

/**
 * Create a Sentry checkpoint for debugging
 */
export function checkpoint(name: string, data?: Record<string, any>) {
  addBreadcrumb(`Checkpoint: ${name}`, 'checkpoint', 'debug', data);
}

/**
 * Log API call for monitoring
 */
export function logApiCall(
  method: string,
  url: string,
  status?: number,
  duration?: number
) {
  addBreadcrumb(
    `API ${method} ${url}`,
    'http',
    status && status >= 400 ? 'error' : 'info',
    {
      method,
      url,
      status,
      duration,
    }
  );
}

/**
 * Log user action for debugging
 */
export function logUserAction(action: string, data?: Record<string, any>) {
  addBreadcrumb(`User action: ${action}`, 'user', 'info', data);
}

/**
 * Configure Sentry scope with common context (v8 compatible)
 */
export function configureScope(callback: (scope: Sentry.Scope) => void) {
  Sentry.withScope(callback);
}

/**
 * Check if Sentry is properly configured
 */
export function isSentryConfigured(): boolean {
  return !!Sentry.getClient();
}

/**
 * Get current Sentry user
 */
export function getCurrentUser(): Sentry.User | undefined {
  let currentUser: Sentry.User | undefined;
  Sentry.withScope((scope) => {
    currentUser = scope.getUser();
  });
  return currentUser;
}

/**
 * Clear user context
 */
export function clearUser() {
  Sentry.setUser(null);
}

/**
 * Flush Sentry events (useful before app termination)
 */
export async function flush(timeout: number = 2000): Promise<boolean> {
  return Sentry.flush(timeout);
}
