import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  
  // Performance Monitoring
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // Environment
  environment: process.env.NODE_ENV,
  
  // Release tracking
  release: process.env.APP_VERSION || 'development',
  
  // Debug mode (only in development)
  debug: process.env.NODE_ENV === 'development',
  
  // Integrations
  integrations: [
    // HTTP integration for tracking HTTP requests
    Sentry.httpIntegration({
      tracing: {
        ignoreIncomingRequests: (url) => {
          // Ignore health check and monitoring endpoints
          return url.includes('/api/health') || 
                 url.includes('/monitoring') ||
                 url.includes('/_next/static') ||
                 url.includes('/favicon.ico');
        },
        ignoreOutgoingRequests: (url) => {
          // Ignore Sentry requests to avoid infinite loops
          return url.includes('sentry.io');
        },
      },
    }),
    
    // Node.js specific integrations
    Sentry.nodeProfilingIntegration(),
  ],
  
  // Error filtering
  beforeSend(event, hint) {
    // Filter out development noise
    if (process.env.NODE_ENV === 'development') {
      // Don't send certain development-only errors
      if (event.exception?.values?.[0]?.value?.includes('ENOENT')) {
        return null;
      }
    }
    
    // Filter out known third-party errors
    if (event.exception?.values?.[0]?.stacktrace?.frames?.some(frame => 
      frame.filename?.includes('node_modules')
    )) {
      // Only send if it's a critical error
      if (event.level !== 'error') {
        return null;
      }
    }
    
    return event;
  },
  
  // Server-specific options
  autoSessionTracking: false, // Disable on server to avoid session conflicts
  sendClientReports: false,
  
  // Profiling (only in production)
  profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0,
  
  // Transport options
  transportOptions: {
    // Increase timeout for server requests
    timeout: 10000,
  },
});
