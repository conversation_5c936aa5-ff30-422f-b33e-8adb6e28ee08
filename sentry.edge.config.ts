import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  
  // Performance Monitoring (lower sample rate for edge)
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.05 : 1.0,
  
  // Environment
  environment: process.env.NODE_ENV,
  
  // Release tracking
  release: process.env.APP_VERSION || 'development',
  
  // Debug mode (only in development)
  debug: process.env.NODE_ENV === 'development',
  
  // Edge-specific integrations (minimal set)
  integrations: [
    // Only basic integrations for edge runtime
  ],
  
  // Error filtering
  beforeSend(event, hint) {
    // Filter out edge-specific noise
    if (process.env.NODE_ENV === 'development') {
      return null; // Don't send from edge in development
    }
    
    return event;
  },
  
  // Edge-specific options
  autoSessionTracking: false,
  sendClientReports: false,
  
  // Minimal transport options for edge
  transportOptions: {
    timeout: 5000, // Shorter timeout for edge
  },
});
