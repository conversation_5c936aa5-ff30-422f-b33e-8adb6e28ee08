import { NextRequest } from 'next/server';

/**
 * Sentry tunnel endpoint to proxy Sentry requests
 * This helps avoid ad-blockers that might block direct requests to Sentry
 */
export async function POST(request: NextRequest) {
  try {
    const envelope = await request.text();
    const pieces = envelope.split('\n');
    const header = JSON.parse(pieces[0]);
    
    // Extract DSN from the header
    const dsn = header.dsn;
    if (!dsn) {
      return new Response('Missing DSN', { status: 400 });
    }
    
    // Parse DSN to get project ID and host
    const dsnUrl = new URL(dsn);
    const projectId = dsnUrl.pathname.substring(1);
    const sentryHost = dsnUrl.host;
    
    // Construct Sentry ingest URL
    const sentryUrl = `https://${sentryHost}/api/${projectId}/envelope/`;
    
    // Forward the request to Sentry
    const response = await fetch(sentryUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-sentry-envelope',
      },
      body: envelope,
    });
    
    return new Response(null, {
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('Sentry tunnel error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
