# Sentry Kurulum Rehberi

Bu dokümanda Sportiva projesine Sentry entegrasyonunun nasıl yapıldığı ve nasıl kullanılacağı açıklanmaktadır.

## 🚀 Kurulum Tamamlandı

Sentry başarıyla projeye entegre edilmiştir. Aşağıdaki dosyalar oluşturuldu/güncellendi:

### Oluşturulan Dosyalar
- `sentry.client.config.ts` - Client-side Sentry konfigürasyonu
- `sentry.server.config.ts` - Server-side Sentry konfigürasyonu  
- `sentry.edge.config.ts` - Edge Runtime Sentry konfigürasyonu
- `src/app/monitoring/route.ts` - Sentry tunnel endpoint (ad-blocker bypass)
- `src/lib/sentry.ts` - Sentry utility fonksiyonları
- `src/app/sentry-example-page/page.tsx` - Test sayfası

### Güncellenen Dosyalar
- `next.config.ts` - Sentry webpack plugin eklendi
- `src/app/error.tsx` - Error boundary Sentry entegrasyonu
- `.env.local` - Sentry environment variables eklendi

## 🔧 Konfigürasyon

### Environment Variables

`.env.local` dosyasında aşağıdaki değişkenleri ayarlayın:

```env
# Sentry Configuration
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ORG=your_sentry_org_here
SENTRY_PROJECT=your_sentry_project_here
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### Sentry Proje Kurulumu

1. [Sentry.io](https://sentry.io) hesabınıza giriş yapın
2. Yeni bir proje oluşturun (Next.js seçin)
3. DSN'inizi kopyalayın
4. Organization ve Project slug'larınızı not alın
5. Auth token oluşturun (Settings > Auth Tokens)

## 🎯 Özellikler

### Otomatik İzleme
- ✅ Unhandled errors
- ✅ Unhandled promise rejections
- ✅ Performance monitoring
- ✅ Session replay (production'da %1, development'ta %10)
- ✅ User context tracking
- ✅ Breadcrumbs
- ✅ Source maps upload

### Manuel İzleme
- ✅ Custom exceptions
- ✅ Custom messages
- ✅ Performance transactions
- ✅ User actions
- ✅ API call monitoring

## 📊 Kullanım Örnekleri

### Hata Yakalama

```typescript
import { captureException } from '@/lib/sentry';

try {
  // Risky operation
} catch (error) {
  captureException(error, {
    tags: { section: 'user-profile' },
    extra: { userId: user.id },
    level: 'error'
  });
}
```

### Mesaj Gönderme

```typescript
import { captureMessage } from '@/lib/sentry';

captureMessage('User completed onboarding', 'info', {
  tags: { feature: 'onboarding' },
  extra: { completionTime: Date.now() }
});
```

### Performance İzleme

```typescript
import { withAsyncPerformance } from '@/lib/sentry';

const fetchUserData = withAsyncPerformance(
  'fetch-user-data',
  async (userId: string) => {
    // API call
  }
);
```

### User Context

```typescript
import { setUser } from '@/lib/sentry';

setUser({
  id: user.id,
  email: user.email,
  username: user.username
});
```

## 🧪 Test Etme

Test sayfasını ziyaret edin: `/sentry-example-page`

Bu sayfada aşağıdaki testleri yapabilirsiniz:
- Hata fırlatma
- Exception yakalama
- Mesaj gönderme
- Breadcrumb ekleme
- User context ayarlama
- Performance testi

## 🔒 Güvenlik

### CSP (Content Security Policy)
Sentry domain'i CSP'ye otomatik olarak eklenmiştir:
- `connect-src` direktifine `https://*.sentry.io` eklendi

### Tunnel Endpoint
Ad-blocker'ları bypass etmek için `/monitoring` endpoint'i oluşturuldu.

## 📈 Production Ayarları

Production ortamında:
- Trace sample rate: %10
- Session replay sample rate: %1
- Error replay sample rate: %100
- Debug mode: kapalı
- Source maps: gizli

## 🛠️ Troubleshooting

### Sentry Çalışmıyor mu?

1. Environment variables'ları kontrol edin
2. DSN'in doğru olduğundan emin olun
3. Console'da Sentry debug loglarını kontrol edin
4. Test sayfasını kullanarak manuel test yapın

### Source Maps Yüklenmiyor mu?

1. `SENTRY_AUTH_TOKEN`'ın doğru olduğundan emin olun
2. Organization ve project slug'larını kontrol edin
3. Build loglarında Sentry plugin çıktısını kontrol edin

## 📚 Daha Fazla Bilgi

- [Sentry Next.js Dokümantasyonu](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
- [Sentry Performance Monitoring](https://docs.sentry.io/product/performance/)
- [Sentry Session Replay](https://docs.sentry.io/product/session-replay/)

## 🎉 Sonuç

Sentry artık projenizde aktif! Hatalar otomatik olarak yakalanacak ve Sentry dashboard'unuzda görünecektir.
