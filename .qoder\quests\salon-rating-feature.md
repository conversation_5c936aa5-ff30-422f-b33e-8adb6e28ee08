# Salon Rating Feature Design

## Overview

This design document outlines the implementation of a salon rating feature that allows registered gym members to rate their gym experience on a scale of 1-5 stars. The feature integrates with the existing gym page structure and review system while adding member-specific validation and rating capabilities.

## Technology Stack & Dependencies

- **Framework**: Next.js 15 with App Router
- **Database**: Supabase (PostgreSQL)
- **UI Components**: shadcn/ui with Tailwind CSS
- **Form Handling**: React Hook Form with Zod validation
- **Authentication**: Supabase Auth
- **State Management**: React Context API

## Component Architecture

### Core Components

#### RatingDialog Component
```mermaid
graph TD
    A[RatingDialog] --> B[StarRating Component]
    A --> C[CommentTextarea]
    A --> D[SubmitButton]
    A --> E[ValidationHandler]
    B --> F[StarInput[]×5]
    E --> G[ZodValidation]
```

**Props Interface**:
- `isOpen: boolean` - Dialog visibility state
- `onOpenChange: (open: boolean) => void` - Dialog state handler
- `gymId: string` - Target gym identifier
- `onRatingSubmit: (rating: RatingData) => void` - Success callback
- `existingRating?: UserRating` - Current user rating if exists

#### Enhanced StarRating Component
```mermaid
graph LR
    A[StarRating] --> B[Interactive Mode]
    A --> C[Display Mode]
    B --> D[onClick Handler]
    B --> E[onHover Effect]
    C --> F[Readonly Stars]
```

**Props Interface**:
- `rating: number` - Current rating value (1-5)
- `onRatingChange?: (rating: number) => void` - Rating change handler
- `readonly?: boolean` - Interactive vs display mode
- `size?: 'sm' | 'md' | 'lg'` - Star size variant
- `showLabel?: boolean` - Display rating labels

#### UserRatingSection Component
```mermaid
graph TD
    A[UserRatingSection] --> B{Has User Rating?}
    B -->|Yes| C[ExistingRating Display]
    B -->|No| D[AddRating Button]
    C --> E[EditRating Button]
    C --> F[DeleteRating Button]
    D --> G[RatingDialog]
    E --> G
```

**Functionality**:
- Displays user's existing rating or prompt to add rating
- Handles rating CRUD operations
- Manages dialog state for rating submission

### Component Hierarchy

```mermaid
graph TD
    A[GymPage] --> B[GymReviewsSection]
    B --> C[UserRatingSection]
    B --> D[ReviewsList]
    C --> E[RatingDialog]
    C --> F[ExistingRatingCard]
    E --> G[StarRating]
    E --> H[CommentForm]
```

## API Integration Layer

### Server Actions

#### createGymRating
```typescript
interface CreateRatingPayload {
  gymId: string;
  rating: number; // 1-5
  comment?: string;
}

interface CreateRatingResponse {
  success: boolean;
  data?: GymReviews;
  error?: string;
}
```

**Business Logic**:
1. Validate user is active gym member
2. Check for existing rating (prevent duplicates)
3. Insert new rating record
4. Update gym aggregate statistics
5. Trigger cache invalidation

#### updateGymRating
```typescript
interface UpdateRatingPayload {
  ratingId: string;
  rating: number;
  comment?: string;
}
```

**Business Logic**:
1. Verify rating ownership
2. Update existing record
3. Recalculate gym statistics
4. Return updated rating data

#### deleteGymRating
```typescript
interface DeleteRatingPayload {
  ratingId: string;
}
```

**Business Logic**:
1. Verify rating ownership
2. Soft delete rating record
3. Update gym aggregate statistics
4. Return success confirmation

#### getUserGymRating
```typescript
interface UserRatingResponse {
  success: boolean;
  data?: {
    id: string;
    rating: number;
    comment: string | null;
    created_at: string;
    updated_at: string;
  };
}
```

**Query Logic**:
- Fetch user's rating for specific gym
- Return null if no rating exists
- Include rating metadata

### Data Flow Architecture

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant ServerAction
    participant Database
    participant Cache

    User->>UI: Click "Rate Gym"
    UI->>ServerAction: Validate membership
    ServerAction->>Database: Check membership status
    Database-->>ServerAction: Return status
    
    alt User is member
        UI->>User: Show rating dialog
        User->>UI: Submit rating
        UI->>ServerAction: createGymRating()
        ServerAction->>Database: Insert rating
        ServerAction->>Database: Update gym stats
        Database-->>ServerAction: Return success
        ServerAction->>Cache: Invalidate related data
        ServerAction-->>UI: Return response
        UI->>User: Show success feedback
    else User not member
        ServerAction-->>UI: Return error
        UI->>User: Show membership required message
    end
```

## Data Models & Database Schema

### Enhanced gym_reviews Table Structure
```sql
-- Existing fields (no changes needed)
id: uuid PRIMARY KEY
gym_id: uuid REFERENCES gyms(id)
profile_id: uuid REFERENCES profiles(id)
rating: integer CHECK (rating >= 1 AND rating <= 5)
comment: text
created_at: timestamptz
updated_at: timestamptz

-- Indexes for performance
CREATE INDEX idx_gym_reviews_gym_id ON gym_reviews(gym_id);
CREATE INDEX idx_gym_reviews_profile_id ON gym_reviews(profile_id);
CREATE UNIQUE INDEX idx_gym_reviews_unique_user_gym ON gym_reviews(gym_id, profile_id);
```

### Updated gyms Table Aggregation Fields
```sql
-- Enhanced aggregate fields in gyms table
average_rating: decimal(3,2) -- e.g., 4.75
review_count: integer DEFAULT 0
rating_distribution: jsonb -- {"1": 2, "2": 1, "3": 5, "4": 12, "5": 20}
```

### TypeScript Interfaces

```typescript
interface UserRating {
  id: string;
  gym_id: string;
  profile_id: string;
  rating: number; // 1-5
  comment: string | null;
  created_at: string;
  updated_at: string;
}

interface RatingFormData {
  rating: number;
  comment?: string;
}

interface GymRatingStats {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: Record<string, number>;
}
```

## Business Logic Layer

### Member Validation Service

```typescript
class MemberValidationService {
  static async validateMembershipForRating(
    gymId: string,
    userId: string
  ): Promise<boolean> {
    // Check active membership status
    // Verify member is not suspended
    // Ensure member has valid package
    return isValidMember;
  }

  static async canUserRate(
    gymId: string, 
    userId: string
  ): Promise<ValidationResult> {
    // Check membership status
    // Verify no existing rating
    // Validate user permissions
    return validationResult;
  }
}
```

### Rating Aggregation Service

```typescript
class RatingAggregationService {
  static async updateGymStatistics(gymId: string): Promise<void> {
    // Calculate new average rating
    // Update total rating count
    // Generate rating distribution
    // Update gym record atomically
  }

  static async recalculateRatings(gymId: string): Promise<GymRatingStats> {
    // Full recalculation for data consistency
    // Used for periodic maintenance
    return updatedStats;
  }
}
```

### Rating Validation Rules

1. **Membership Requirement**: Only active gym members can submit ratings
2. **One Rating Per User**: Each user can only have one rating per gym
3. **Rating Range**: Ratings must be between 1-5 stars
4. **Comment Validation**: Comments are optional, max 500 characters
5. **Rate Limiting**: Users can update their rating once per 24 hours
6. **Content Moderation**: Comments are subject to content guidelines

## UI/UX Architecture & Styling Strategy

### Visual Design Patterns

#### Rating States
```mermaid
stateDiagram-v2
    [*] --> NoRating
    NoRating --> RatingDialog : Click "Rate Gym"
    RatingDialog --> Submitting : Submit rating
    Submitting --> HasRating : Success
    Submitting --> RatingDialog : Error
    HasRating --> EditDialog : Edit rating
    EditDialog --> HasRating : Update success
    HasRating --> NoRating : Delete rating
```

#### Star Rating Visual System
- **Empty Stars**: Gray outline with hover effects
- **Filled Stars**: Primary color gradient
- **Interactive Feedback**: Smooth animations on hover/click
- **Accessibility**: ARIA labels and keyboard navigation
- **Size Variants**: Small (16px), Medium (24px), Large (32px)

#### Rating Dialog Design
```css
/* Tailwind CSS classes structure */
.rating-dialog {
  @apply fixed inset-0 z-50 bg-background/80 backdrop-blur-sm;
}

.rating-content {
  @apply bg-card rounded-3xl p-8 shadow-2xl border border-border/50;
  @apply max-w-md mx-auto mt-[10vh];
}

.star-container {
  @apply flex items-center justify-center gap-2 my-6;
}

.rating-star {
  @apply w-12 h-12 cursor-pointer transition-all duration-200;
  @apply hover:scale-110 focus:scale-110;
}
```

### Responsive Design Strategy

#### Mobile-First Approach
- **Touch-Friendly**: Minimum 44px touch targets for stars
- **Gesture Support**: Swipe gestures for star selection
- **Bottom Sheet**: Rating dialog as bottom sheet on mobile
- **Compact Layout**: Optimized spacing for small screens

#### Desktop Enhancements
- **Hover Effects**: Rich hover states for better feedback
- **Keyboard Navigation**: Tab through stars, Enter to select
- **Tooltip Labels**: Rating descriptions on hover
- **Modal Positioning**: Centered modal with backdrop

### Animation Framework

#### Micro-Interactions
```css
/* Star hover animation */
.star-hover {
  @apply transform transition-all duration-200 ease-out;
  @apply hover:scale-110 hover:rotate-12;
}

/* Rating submission success */
.rating-success {
  @apply animate-bounce transform;
  animation: rating-success 0.6s ease-out;
}

/* Error state shake */
.rating-error {
  animation: shake 0.4s ease-in-out;
}
```

## Testing Strategy

### Unit Testing Scope

#### Component Tests
- StarRating component interactions
- RatingDialog form validation
- UserRatingSection state management
- Form submission handling

#### API Action Tests
- Member validation logic
- Rating CRUD operations
- Aggregate calculation accuracy
- Error handling scenarios

### Integration Testing

#### User Workflow Tests
1. **Happy Path**: Member rates gym successfully
2. **Update Flow**: Member updates existing rating
3. **Delete Flow**: Member removes their rating
4. **Validation Flow**: Non-member attempts to rate
5. **Error Handling**: Network failures and retries

#### Database Integration
- Rating uniqueness constraints
- Aggregate update consistency
- Transaction rollback scenarios
- Performance under load

### E2E Testing Scenarios

```typescript
// Playwright test example
test('Member can rate gym', async ({ page }) => {
  // Login as gym member
  await loginAsMember(page);
  
  // Navigate to gym page
  await page.goto('/gym/test-gym-slug');
  
  // Click rate gym button
  await page.click('[data-testid="rate-gym-button"]');
  
  // Select 5 stars
  await page.click('[data-testid="star-5"]');
  
  // Add comment
  await page.fill('[data-testid="rating-comment"]', 'Great gym!');
  
  // Submit rating
  await page.click('[data-testid="submit-rating"]');
  
  // Verify success message
  await expect(page.locator('[data-testid="rating-success"]')).toBeVisible();
});
```

## Performance & Optimization

### Database Optimization
- **Indexed Queries**: Optimize rating lookups by gym_id and profile_id
- **Aggregate Caching**: Cache calculated statistics in gym table
- **Batch Updates**: Process multiple rating updates efficiently
- **Read Replicas**: Use read replicas for rating display queries

### Frontend Optimization
- **Component Memoization**: Memoize StarRating and review components
- **Lazy Loading**: Load rating dialog components on demand
- **Debounced Updates**: Debounce rating slider interactions
- **Optimistic Updates**: Show immediate feedback before server confirmation

### Caching Strategy
- **Page-Level**: Cache gym page data including ratings
- **Component-Level**: Cache user's existing rating status
- **API-Level**: Cache aggregated rating statistics
- **CDN**: Distribute rating images and icons via CDN

## Security Considerations

### Authentication & Authorization
- **JWT Validation**: Verify user tokens on all rating operations
- **Membership Verification**: Confirm active membership before allowing ratings
- **Rate Limiting**: Implement rate limiting to prevent spam ratings
- **CSRF Protection**: Use CSRF tokens for form submissions

### Data Validation
- **Server-Side Validation**: Validate all rating data on server
- **SQL Injection Prevention**: Use parameterized queries
- **XSS Protection**: Sanitize comment inputs
- **Input Constraints**: Enforce rating range and comment length limits

### Privacy & Compliance
- **Data Anonymization**: Option to display ratings anonymously
- **GDPR Compliance**: Support rating data deletion requests
- **Audit Logging**: Log all rating operations for security auditing
- **Content Moderation**: Implement automated content filtering
