import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  
  // Performance Monitoring
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // Session Replay
  replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.01 : 0.1,
  replaysOnErrorSampleRate: 1.0,
  
  // Environment
  environment: process.env.NODE_ENV,
  
  // Release tracking
  release: process.env.NEXT_PUBLIC_APP_VERSION || 'development',
  
  // Debug mode (only in development)
  debug: process.env.NODE_ENV === 'development',
  
  // Integrations
  integrations: [
    Sentry.replayIntegration({
      // Mask all text content, record only clicks, inputs, and navigation
      maskAllText: true,
      blockAllMedia: true,
    }),
    Sentry.browserTracingIntegration({
      // Set up automatic route change tracking for Next.js App Router
      instrumentNavigation: true,
      instrumentPageLoad: true,
    }),
  ],
  
  // Error filtering
  beforeSend(event) {
    // Filter out development errors
    if (process.env.NODE_ENV === 'development') {
      // Don't send hydration errors in development
      if (event.exception?.values?.[0]?.value?.includes('Hydration')) {
        return null;
      }
    }
    
    // Filter out network errors
    if (event.exception?.values?.[0]?.type === 'NetworkError') {
      return null;
    }
    
    // Filter out ResizeObserver errors (common browser quirk)
    if (event.exception?.values?.[0]?.value?.includes('ResizeObserver')) {
      return null;
    }
    
    return event;
  },
  
  // Additional options - removed autoSessionTracking and sendClientReports as they're not available in v8
  
  // Transport options - removed fetchParameters as it's not available in v8
});
